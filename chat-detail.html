<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天详情</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            padding-top: 30px;
            padding-bottom: 70px;
            background-color: #f5f7fa;
        }
        .status-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 30px;
            z-index: 20;
            padding: 5px 15px;
        }
        .message-bubble {
            max-width: 75%;
            border-radius: 18px;
            padding: 10px 16px;
            margin-bottom: 8px;
            position: relative;
        }
        .message-me {
            background-color: #0084ff;
            color: white;
            border-bottom-right-radius: 4px;
            margin-left: auto;
        }
        .message-other {
            background-color: #e4e6eb;
            color: #1c1e21;
            border-bottom-left-radius: 4px;
            margin-right: auto;
        }
        .message-time {
            font-size: 11px;
            margin-bottom: 12px;
            text-align: center;
            color: #65676b;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar flex justify-between items-center bg-white">
        <div class="text-sm">9:41</div>
        <div class="flex space-x-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>
    
    <!-- 顶部导航 -->
    <div class="fixed top-8 left-0 right-0 z-10 bg-white border-b border-gray-200">
        <div class="flex justify-between items-center px-4 py-3">
            <div class="flex items-center">
                <a href="chat.html" class="mr-3">
                    <i class="fas fa-arrow-left text-xl text-gray-600"></i>
                </a>
                <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330" alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                <div>
                    <div class="font-medium">李华</div>
                    <div class="text-xs text-gray-500">在线</div>
                </div>
            </div>
            <div class="flex space-x-6">
                <i class="fas fa-user text-gray-600"></i>
            </div>
        </div>
    </div>
    
    <!-- 聊天内容区 -->
    <div class="mt-24 mb-16 px-4">
        <!-- 时间标记 -->
        <div class="message-time">今天 12:20</div>
        
        <!-- 对方消息 -->
        <div class="flex items-end mb-4">
            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330" alt="用户头像" class="w-8 h-8 rounded-full mr-2 mb-1">
            <div class="message-bubble message-other">
                <p>你好！看到你分享的餐厅照片很不错，想请教一下，那家店周末需要提前预约吗？</p>
            </div>
        </div>
        
        <!-- 我的消息 -->
        <div class="flex justify-end mb-4">
            <div class="message-bubble message-me">
                <p>你好！是的，周末最好提前一天预约，那家店晚上很火爆，经常要排队呢！</p>
            </div>
        </div>
        
        <!-- 对方消息 -->
        <div class="flex items-end mb-4">
            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330" alt="用户头像" class="w-8 h-8 rounded-full mr-2 mb-1">
            <div class="message-bubble message-other">
                <p>谢谢提醒！人均消费大概是多少呢？有什么招牌菜推荐吗？</p>
            </div>
        </div>
        
        <!-- 时间标记 -->
        <div class="message-time">12:25</div>
        
        <!-- 我的消息 -->
        <div class="flex justify-end mb-4">
            <div class="message-bubble message-me">
                <p>人均大概150左右。强烈推荐他们家的金牌烤鸭和芒果千层蛋糕！</p>
            </div>
        </div>
        
        <!-- 我的消息 - 图片 -->
        <div class="flex justify-end mb-4">
            <div class="message-bubble message-me p-1">
                <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836" alt="餐厅美食图片" class="rounded-md w-48">
            </div>
        </div>
        
        <!-- 对方消息 -->
        <div class="flex items-end mb-4">
            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330" alt="用户头像" class="w-8 h-8 rounded-full mr-2 mb-1">
            <div class="message-bubble message-other">
                <p>哇，看起来太美味了！谢谢你的推荐，周末我也去尝试一下！</p>
            </div>
        </div>
        
        <!-- 时间标记 -->
        <div class="message-time">12:30</div>
        
        <!-- 对方消息 -->
        <div class="flex items-end">
            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330" alt="用户头像" class="w-8 h-8 rounded-full mr-2 mb-1">
            <div class="message-bubble message-other">
                <p>对了，你那篇帖子里提到的另一家咖啡店在哪里？有机会也想去尝试一下。</p>
            </div>
        </div>
    </div>
    
    <!-- 底部输入框 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex items-center">
            <button class="p-2 text-gray-500">
                <i class="far fa-grin text-xl"></i>
            </button>
            <div class="flex-1 bg-gray-100 rounded-full px-4 py-2 mx-2 flex items-center">
                <input type="text" placeholder="发送消息..." class="bg-transparent w-full border-none focus:ring-0">
            </div>
            <button class="p-2 text-gray-500">
                <i class="fas fa-plus text-xl"></i>
            </button>
            <button class="ml-2 p-2 text-blue-500">
                <i class="far fa-paper-plane text-xl"></i>
            </button>
        </div>
        
        <!-- 扩展功能面板(默认隐藏) -->
        <div class="hidden grid grid-cols-4 gap-4 p-4">
            <div class="flex flex-col items-center">
                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                    <i class="fas fa-camera text-gray-600"></i>
                </div>
                <span class="text-xs mt-1 text-gray-600">相册</span>
            </div>
            <div class="flex flex-col items-center">
                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                    <i class="fas fa-camera text-gray-600"></i>
                </div>
                <span class="text-xs mt-1 text-gray-600">拍摄</span>
            </div>
            <div class="flex flex-col items-center">
                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                    <i class="fas fa-map-marker-alt text-gray-600"></i>
                </div>
                <span class="text-xs mt-1 text-gray-600">位置</span>
            </div>
            <div class="flex flex-col items-center">
                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                    <i class="fas fa-file text-gray-600"></i>
                </div>
                <span class="text-xs mt-1 text-gray-600">文件</span>
            </div>
        </div>
    </div>
</body>
</html> 