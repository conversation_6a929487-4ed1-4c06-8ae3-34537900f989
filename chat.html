<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息</title>
    <link rel="stylesheet" href="design-system.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 页面特定样式 */
        .chat-tabs {
            background-color: var(--bg-primary);
            border-bottom: 1px solid var(--border-light);
            display: flex;
        }

        .chat-tab {
            flex: 1;
            padding: var(--space-4);
            text-align: center;
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: var(--font-medium);
            font-size: var(--text-sm);
            border-bottom: 2px solid transparent;
            transition: all var(--transition-fast);
            margin-top: 100px;
        }

        .chat-tab:hover {
            color: var(--accent-500);
            background-color: var(--accent-50);
        }

        .chat-tab.active {
            color: var(--accent-600);
            border-bottom-color: var(--accent-500);
            background-color: var(--accent-50);
        }

        .search-container {
            padding: var(--space-4);
            background-color: var(--bg-primary);
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: var(--space-3) var(--space-3) var(--space-3) var(--space-10);
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            transition: all var(--transition-fast);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent-500);
            background-color: var(--bg-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-icon {
            position: absolute;
            left: var(--space-3);
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-tertiary);
        }

        .message-list {
            background-color: var(--bg-primary);
        }

        .message-item {
            display: flex;
            align-items: center;
            padding: var(--space-4);
            border-bottom: 1px solid var(--border-light);
            transition: background-color var(--transition-fast);
        }

        .message-item:hover {
            background-color: var(--bg-tertiary);
        }

        .message-item:last-child {
            border-bottom: none;
        }

        .avatar-container {
            position: relative;
            margin-right: var(--space-3);
        }

        .avatar {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-full);
            object-fit: cover;
        }

        .system-avatar, .group-avatar {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--text-lg);
        }

        .system-avatar {
            background: linear-gradient(135deg, var(--accent-500), var(--accent-600));
        }

        .group-avatar {
            background: linear-gradient(135deg, #a855f7, #ec4899);
        }

        .unread-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: var(--error);
            color: white;
            border-radius: var(--radius-full);
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--text-xs);
            font-weight: var(--font-semibold);
            border: 2px solid var(--bg-primary);
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-1);
        }

        .sender-name {
            font-weight: var(--font-semibold);
            color: var(--text-primary);
            font-size: var(--text-base);
        }

        .message-time {
            font-size: var(--text-xs);
            color: var(--text-tertiary);
        }

        .message-preview {
            color: var(--text-secondary);
            font-size: var(--text-sm);
            line-height: var(--leading-tight);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .message-preview.unread {
            color: var(--text-primary);
            font-weight: var(--font-medium);
        }

        .notifications-section {
            padding: var(--space-4);
        }

        .section-title {
            font-size: var(--text-sm);
            color: var(--text-tertiary);
            font-weight: var(--font-medium);
            margin-bottom: var(--space-3);
        }

        .notification-item {
            background-color: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-3);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .notification-icon {
            width: 36px;
            height: 36px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--text-sm);
        }

        .like-icon {
            background-color: var(--error);
        }

        .follow-icon {
            background-color: var(--accent-500);
        }

        .notification-content {
            flex: 1;
        }

        .notification-text {
            font-size: var(--text-sm);
            color: var(--text-primary);
            line-height: var(--leading-normal);
            margin-bottom: var(--space-1);
        }

        .notification-date {
            font-size: var(--text-xs);
            color: var(--text-tertiary);
        }

        .follow-btn {
            background-color: var(--accent-500);
            color: white;
            border: none;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-full);
            font-size: var(--text-xs);
            font-weight: var(--font-semibold);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .follow-btn:hover {
            background-color: var(--accent-600);
            transform: translateY(-1px);
        }

        .navbar-title {
            font-size: var(--text-lg);
            font-weight: var(--font-bold);
            color: var(--text-primary);
        }

        .action-btn {
            padding: var(--space-2);
            color: var(--text-secondary);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
            border: none;
            background: none;
            cursor: pointer;
        }

        .action-btn:hover {
            color: var(--accent-500);
            background-color: var(--accent-50);
        }
        .content {
            padding-top: 0;
        }
    </style>
</head>
<body class="bg-white">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="text-sm font-medium">9:41</div>
        <div class="flex items-center gap-2">
            <i class="fas fa-signal">1</i>
            <i class="fas fa-wifi">2</i>
            <i class="fas fa-battery-full">3</i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="navbar">
        <div class="flex items-center justify-between">
            <div class="navbar-title">消息</div>
            <button class="action-btn">
                <i class="fas fa-edit text-lg"></i>
            </button>
        </div>
    </div>

    <!-- 分类标签 -->
    <div class="chat-tabs">
        <a href="#" class="chat-tab active">聊天</a>
        <a href="#" class="chat-tab">通知</a>
        <a href="#" class="chat-tab">点赞</a>
    </div>
    
    <!-- 消息列表 -->
    <div class="content">
        <!-- 消息搜索框 -->
        <div class="search-container">
            <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <input type="text" placeholder="搜索聊天记录" class="search-input">
            </div>
        </div>

        <!-- 消息列表 -->
        <div class="message-list">
            <!-- 消息项1 - 未读 -->
            <div class="message-item">
                <div class="avatar-container">
                    <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330" alt="用户头像" class="avatar">
                    <div class="unread-badge">2</div>
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <div class="sender-name">李华</div>
                        <div class="message-time">12:30</div>
                    </div>
                    <div class="message-preview unread">你好！看到你分享的餐厅照片很不错，想请教一下...</div>
                </div>
            </div>
        
            <!-- 消息项2 -->
            <div class="message-item">
                <div class="avatar-container">
                    <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36" alt="用户头像" class="avatar">
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <div class="sender-name">王小鹏</div>
                        <div class="message-time">昨天</div>
                    </div>
                    <div class="message-preview">下周一我们一起去那家餐厅吧！[图片]</div>
                </div>
            </div>

            <!-- 消息项3 - 已读 -->
            <div class="message-item">
                <div class="avatar-container">
                    <img src="https://images.unsplash.com/photo-1570295999919-56ceb5ecca61" alt="用户头像" class="avatar">
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <div class="sender-name">美食达人</div>
                        <div class="message-time">周一</div>
                    </div>
                    <div class="message-preview">感谢关注我的账号！我每周都会分享新的美食推荐~</div>
                </div>
            </div>

            <!-- 消息项4 - 官方通知 -->
            <div class="message-item">
                <div class="avatar-container">
                    <div class="system-avatar">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <div class="sender-name">系统通知</div>
                        <div class="message-time">7月15日</div>
                    </div>
                    <div class="message-preview">您的账号已升级为认证用户，解锁更多专属功能！</div>
                </div>
            </div>

            <!-- 群聊消息 -->
            <div class="message-item">
                <div class="avatar-container">
                    <div class="group-avatar">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="unread-badge">5</div>
                </div>
                <div class="message-content">
                    <div class="message-header">
                        <div class="sender-name">美食爱好者群</div>
                        <div class="message-time">7月10日</div>
                    </div>
                    <div class="message-preview unread">赵丽: 我也去过那家餐厅，他们家的甜点超级赞！</div>
                </div>
            </div>
        </div>
        
        <!-- 通知信息 -->
        <div class="notifications-section">
            <div class="section-title">更早</div>

            <div class="notification-item">
                <div class="notification-icon like-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-text">
                        <span class="font-semibold">王小鹏、李华</span>等5人赞了你的帖子
                        《周末去了新开的那家网红餐厅...》
                    </div>
                    <div class="notification-date">7月8日</div>
                </div>
            </div>

            <div class="notification-item">
                <div class="notification-icon follow-icon">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-text">
                        <span class="font-semibold">赵丽</span>关注了你
                    </div>
                    <div class="notification-date">7月5日</div>
                </div>
                <button class="follow-btn">回关</button>
            </div>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="post.html" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="discover.html" class="nav-item">
            <i class="fas fa-fire"></i>
            <span>热门</span>
        </a>
        <a href="add-post.html" class="nav-item">
            <div class="fab-btn">
                <i class="fas fa-plus"></i>
            </div>
        </a>
        <a href="chat.html" class="nav-item active">
            <i class="fas fa-comment-dots"></i>
            <span>消息</span>
        </a>
        <a href="me.html" class="nav-item">
            <i class="far fa-user"></i>
            <span>我的</span>
        </a>
    </nav>
</body>
</html> 