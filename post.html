<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帖子流</title>
    <link rel="stylesheet" href="design-system.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 页面特定样式 */
        .navbar-tabs {
            display: flex;
            justify-content: center;
            gap: var(--space-6);
            margin-top: var(--space-3);
        }

        .nav-tab {
            padding: var(--space-3) var(--space-4);
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: var(--font-medium);
            font-size: var(--text-sm);
            border-radius: var(--radius-lg);
            transition: all var(--transition-fast);
            position: relative;
        }

        .nav-tab:hover {
            color: var(--accent-500);
            background-color: var(--accent-50);
        }

        .nav-tab.active {
            color: var(--accent-600);
            background-color: var(--accent-50);
            font-weight: var(--font-semibold);
        }

        .nav-tab.active::after {
            content: "";
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 2px;
            background-color: var(--accent-500);
            border-radius: var(--radius-full);
        }

        .posts-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--space-4);
            padding: var(--space-4);
        }

        .post-card {
            background-color: var(--bg-primary);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
            transition: all var(--transition-normal);
            aspect-ratio: 0.75;
            display: flex;
            flex-direction: column;
        }

        .post-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .post-image-container {
            position: relative;
            flex: 1;
            overflow: hidden;
        }

        .post-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform var(--transition-normal);
        }

        .post-card:hover .post-image {
            transform: scale(1.02);
        }

        .video-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 32px;
            height: 32px;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--text-sm);
        }

        .post-info {
            padding: var(--space-3);
            background-color: var(--bg-primary);
        }

        .post-title {
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--text-primary);
            margin-bottom: var(--space-2);
            line-height: var(--leading-tight);
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .post-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .user-avatar {
            width: 16px;
            height: 16px;
            border-radius: var(--radius-full);
            object-fit: cover;
        }

        .user-name {
            font-size: var(--text-xs);
            color: var(--text-secondary);
            font-weight: var(--font-medium);
        }

        .like-info {
            display: flex;
            align-items: center;
            gap: var(--space-1);
            color: var(--text-tertiary);
            font-size: var(--text-xs);
        }

        .like-info i {
            transition: color var(--transition-fast);
        }

        .like-info:hover i {
            color: var(--error);
        }

        .navbar-actions {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .action-btn {
            padding: var(--space-2);
            color: var(--text-secondary);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
            border: none;
            background: none;
            cursor: pointer;
        }

        .action-btn:hover {
            color: var(--accent-500);
            background-color: var(--accent-50);
        }

        .fab-btn {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--accent-500), var(--accent-600));
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: var(--text-lg);
            box-shadow: var(--shadow-lg);
            transition: all var(--transition-normal);
        }

        .fab-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }
        .content {
            padding-top: 0;
        }
        .content-tabs {
            background-color: var(--bg-primary);
            border-top: 1px solid var(--border-light);
            border-bottom: 1px solid var(--border-light);
            display: flex;
            justify-content:left;
            margin-top: 130px;
            padding: 10px;
        }
        .tab-button {
            background-color: #f8f6f6;
            border: none;
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: var(--text-lg);
            margin: 0 5px;
            border-radius: 5px;
            border: 1px solid var(--border-light);
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="text-sm font-medium">9:41</div>
        <div class="flex items-center gap-2">
            <span class="text-xs">5G</span>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="navbar">
        <div class="flex items-center justify-between">
            <button class="action-btn">
                <i class="fas fa-bars text-lg"></i>
            </button>
            <div class="navbar-tabs">
                <a href="#" class="nav-tab">关注</a>
                <a href="#" class="nav-tab active">发现</a>
                <a href="#" class="nav-tab">广州</a>
            </div>
            <button class="action-btn">
                <i class="fas fa-search text-lg"></i>
            </button>
        </div>
    </div>

    <div class="content-tabs">
        <button class="tab-button active">
            找搭子
        </button>
        <button class="tab-button">
            意见反馈
        </button>
        <button class="tab-button">
            兴趣爱好
        </button>
    </div>
    
    <!-- 内容区 -->
    <div class="content">
        <div class="posts-grid">
            <!-- 帖子1 -->
            <article class="post-card">
                <div class="post-image-container">
                    <img src="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4" alt="帖子图片" class="post-image">
                </div>
                <div class="post-info">
                    <h3 class="post-title">你喜欢什么舞蹈呢？</h3>
                    <div class="post-meta">
                        <div class="user-info">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" alt="用户头像" class="user-avatar">
                            <span class="user-name">张小明</span>
                        </div>
                        <div class="like-info">
                            <i class="far fa-heart"></i>
                            <span>219</span>
                        </div>
                    </div>
                </div>
            </article>

            <!-- 帖子2 -->
            <article class="post-card">
                <div class="post-image-container">
                    <img src="https://images.unsplash.com/photo-1517457373958-b7bdd4587205" alt="视频封面" class="post-image">
                    <div class="video-indicator">
                        <i class="fas fa-play"></i>
                    </div>
                </div>
                <div class="post-info">
                    <h3 class="post-title">这就是fox的afro课？</h3>
                    <div class="post-meta">
                        <div class="user-info">
                            <img src="https://images.unsplash.com/photo-1570295999919-56ceb5ecca61" alt="用户头像" class="user-avatar">
                            <span class="user-name">进击的牛牛</span>
                        </div>
                        <div class="like-info">
                            <i class="far fa-heart"></i>
                            <span>16</span>
                        </div>
                    </div>
                </div>
            </article>

            <!-- 帖子3 -->
            <article class="post-card">
                <div class="post-image-container">
                    <img src="https://images.unsplash.com/photo-1555507036-ab1f4038808a" alt="帖子图片" class="post-image">
                </div>
                <div class="post-info">
                    <h3 class="post-title">特角章</h3>
                    <div class="post-meta">
                        <div class="user-info">
                            <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36" alt="用户头像" class="user-avatar">
                            <span class="user-name">王小鹏</span>
                        </div>
                        <div class="like-info">
                            <i class="far fa-heart"></i>
                            <span>32</span>
                        </div>
                    </div>
                </div>
            </article>

            <!-- 帖子4 -->
            <article class="post-card">
                <div class="post-image-container">
                    <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836" alt="帖子图片" class="post-image">
                </div>
                <div class="post-info">
                    <h3 class="post-title">多少钱才能财富自由？</h3>
                    <div class="post-meta">
                        <div class="user-info">
                            <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330" alt="用户头像" class="user-avatar">
                            <span class="user-name">帅帅睡不醒</span>
                        </div>
                        <div class="like-info">
                            <i class="far fa-heart"></i>
                            <span>3157</span>
                        </div>
                    </div>
                </div>
            </article>
        </div>
    </div>
    
    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="post.html" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="discover.html" class="nav-item">
            <i class="fas fa-fire"></i>
            <span>热门</span>
        </a>
        <a href="add-post.html" class="nav-item">
            <div class="fab-btn">
                <i class="fas fa-plus"></i>
            </div>
        </a>
        <a href="chat.html" class="nav-item">
            <i class="far fa-comment-dots"></i>
            <span>消息</span>
        </a>
        <a href="me.html" class="nav-item">
            <i class="far fa-user"></i>
            <span>我的</span>
        </a>
    </nav>
</body>
</html> 