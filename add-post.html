<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发布帖子</title>
    <link rel="stylesheet" href="design-system.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 页面特定样式 */
        .post-form {
            background-color: var(--bg-primary);
            border-radius: var(--radius-xl);
            margin: var(--space-4);
            padding: var(--space-6);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-light);
        }
        .text {
            width: 100%;
            min-height: 60px;
            padding: var(--space-4);
            border: 1px solid var(--border-medium);
            border-radius: var(--radius-lg);
            background-color: var(--bg-primary);
            resize: none;
            font-size: var(--text-base);
            line-height: var(--leading-normal);
            color: var(--text-primary);
            transition: all var(--transition-fast);
        }

        .text-area {
            width: 100%;
            min-height: 120px;
            padding: var(--space-4);
            border: 1px solid var(--border-medium);
            border-radius: var(--radius-lg);
            background-color: var(--bg-primary);
            resize: none;
            font-size: var(--text-base);
            line-height: var(--leading-normal);
            color: var(--text-primary);
            transition: all var(--transition-fast);
        }

        .text-area:focus {
            outline: none;
            border-color: var(--accent-500);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .text-area::placeholder {
            color: var(--text-tertiary);
        }

        .media-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--space-3);
            margin: var(--space-6) 0;
        }

        .media-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .media-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-button {
            position: absolute;
            top: var(--space-2);
            right: var(--space-2);
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .delete-button:hover {
            background-color: var(--error);
        }

        .upload-placeholder {
            border: 2px dashed var(--border-medium);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            aspect-ratio: 1;
            background-color: var(--bg-tertiary);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .upload-placeholder:hover {
            border-color: var(--accent-500);
            background-color: var(--accent-50);
        }

        .options-list {
            margin-top: var(--space-6);
        }

        .option-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--space-4) 0;
            border-bottom: 1px solid var(--border-light);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .option-item:hover {
            background-color: var(--accent-50);
            margin: 0 calc(-1 * var(--space-6));
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-md);
        }

        .option-item:last-child {
            border-bottom: none;
        }

        .option-left {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }

        .option-icon {
            color: var(--accent-500);
            font-size: var(--text-lg);
        }

        .option-text {
            color: var(--text-primary);
            font-weight: var(--font-medium);
        }

        .option-value {
            color: var(--text-secondary);
            font-size: var(--text-sm);
            margin-right: var(--space-2);
        }

        .toolbar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--bg-primary);
            border-top: 1px solid var(--border-light);
            padding: var(--space-4);
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .toolbar-btn {
            width: 44px;
            height: 44px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--accent-50);
            border: none;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .toolbar-btn:hover {
            background-color: var(--accent-100);
            transform: scale(1.05);
        }

        .toolbar-btn i {
            color: var(--accent-600);
            font-size: var(--text-lg);
        }

        .navbar-title {
            font-size: var(--text-lg);
            font-weight: var(--font-bold);
            color: var(--text-primary);
        }

        .close-btn {
            padding: var(--space-2);
            color: var(--text-secondary);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
            border: none;
            background: none;
            cursor: pointer;
        }

        .close-btn:hover {
            color: var(--text-primary);
            background-color: var(--bg-tertiary);
        }

        .publish-btn {
            background-color: var(--accent-500);
            color: white;
            border: none;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-full);
            font-weight: var(--font-semibold);
            font-size: var(--text-sm);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .publish-btn:hover {
            background-color: var(--accent-600);
            transform: translateY(-1px);
        }
    </style>
</head>
<body class="bg-white">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div class="text-sm font-medium">9:41</div>
        <div class="flex items-center gap-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- 顶部导航 -->
    <div class="navbar">
        <div class="flex items-center justify-between">
            <a href="post.html" class="close-btn">
                <i class="fas fa-times text-lg"></i>
            </a>
            <div class="navbar-title">发布帖子</div>
            <button class="publish-btn">发布</button>
        </div>
    </div>
    
    <!-- 内容区 -->
    <div class="content">
        <div class="post-form">
            <!-- 文本输入区 -->
            <div class="mb-6">
                <input placeholder="标题..." class="text"></input>
            </div>
            <!-- 文本输入区 -->
            <div class="mb-6">
                <textarea placeholder="分享你的想法..." class="text-area"></textarea>
            </div>

            <!-- 媒体上传区 -->
            <div class="media-grid">
                <!-- 已上传图片1 -->
                <div class="media-item">
                    <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836" alt="预览图片" class="media-preview">
                    <button class="delete-button">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>

                <!-- 已上传图片2 -->
                <div class="media-item">
                    <img src="https://images.unsplash.com/photo-1512621776951-a57141f2eefd" alt="预览图片" class="media-preview">
                    <button class="delete-button">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>

                <!-- 上传按钮 -->
                <div class="upload-placeholder">
                    <i class="fas fa-plus text-gray-400 text-xl"></i>
                </div>
            </div>

            <!-- 选项列表 -->
            <div class="options-list">
                <!-- 定位信息 -->
                <div class="option-item">
                    <div class="option-left">
                        <i class="fas fa-map-marker-alt option-icon"></i>
                        <span class="option-text">添加位置</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-300"></i>
                </div>

                <!-- 话题标签 -->
                <div class="option-item">
                    <div class="option-left">
                        <i class="fas fa-hashtag option-icon"></i>
                        <span class="option-text">添加话题</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-300"></i>
                </div>

            </div>
        </div>
    </div>
    
    <!-- 底部工具栏 -->
    <div class="toolbar">
        <button class="toolbar-btn">
            <i class="fas fa-image"></i>
        </button>
        <button class="toolbar-btn">
            <i class="fas fa-camera"></i>
        </button>
        <button class="toolbar-btn">
            <i class="fas fa-video"></i>
        </button>
        <button class="toolbar-btn">
            <i class="far fa-smile"></i>
        </button>
        <button class="toolbar-btn">
            <i class="fas fa-at"></i>
        </button>
    </div>
</body>
</html> 