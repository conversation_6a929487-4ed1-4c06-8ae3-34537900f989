<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帖子分享应用设计总览</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: #f0f0f0;
        }
        .page-container {
            width: 280px;
            height: 560px;
            background-color: #fff;
            border-radius: 36px;
            padding: 8px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            margin: 20px;
        }
        .page-screen {
            width: 100%;
            height: 100%;
            background-color: #fff;
            border-radius: 30px;
            overflow: hidden;
            position: relative;
        }
        .notch {
            position: absolute;
            width: 85px;
            height: 25px;
            background-color: #000;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            border-bottom-left-radius: 14px;
            border-bottom-right-radius: 14px;
            z-index: 10;
        }
        .page-title {
            font-weight: bold;
            margin-bottom: 8px;
            text-align: center;
            font-size: 16px;
            color: #333;
        }
        .iframe-container {
            width: 100%;
            height: 100%;
            border: none;
            transform: scale(0.7);
            transform-origin: 0 0;
            width: 142.85%; /* 1/0.7 = 1.4285... */
            height: 142.85%;
        }
    </style>
</head>
<body class="p-8">
    <h1 class="text-3xl font-bold text-center mb-8">帖子分享应用 - 设计总览</h1>
    <div class="flex flex-wrap justify-center">
        <!-- 帖子流主页 -->
        <div class="flex flex-col">
            <div class="page-title">帖子流主页</div>
            <div class="page-container">
                <div class="page-screen">
                    <div class="notch"></div>
                    <iframe src="post.html" class="iframe-container" title="帖子流主页"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 发布帖子页面 -->
        <div class="flex flex-col">
            <div class="page-title">发布帖子页面</div>
            <div class="page-container">
                <div class="page-screen">
                    <div class="notch"></div>
                    <iframe src="add-post.html" class="iframe-container" title="发布帖子页面"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 帖子详情页 -->
        <div class="flex flex-col">
            <div class="page-title">帖子详情页</div>
            <div class="page-container">
                <div class="page-screen">
                    <div class="notch"></div>
                    <iframe src="post-detail.html" class="iframe-container" title="帖子详情页"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 个人中心 -->
        <div class="flex flex-col">
            <div class="page-title">个人中心</div>
            <div class="page-container">
                <div class="page-screen">
                    <div class="notch"></div>
                    <iframe src="me.html" class="iframe-container" title="个人中心"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 聊天消息页面 -->
        <div class="flex flex-col">
            <div class="page-title">聊天消息页面</div>
            <div class="page-container">
                <div class="page-screen">
                    <div class="notch"></div>
                    <iframe src="chat.html" class="iframe-container" title="聊天消息页面"></iframe>
                </div>
            </div>
        </div>
        
        <!-- 聊天详情页 -->
        <div class="flex flex-col">
            <div class="page-title">聊天详情页</div>
            <div class="page-container">
                <div class="page-screen">
                    <div class="notch"></div>
                    <iframe src="chat-detail.html" class="iframe-container" title="聊天详情页"></iframe>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-12 text-center text-gray-600 text-sm">
        <p>设计说明：本应用采用现代UI设计，符合iOS界面规范，包含社区帖子分享、互动、聊天等核心功能。</p>
        <p>通过标签页导航和直观的用户界面，为用户提供流畅的浏览和内容创作体验。</p>
    </div>
</body>
</html> 